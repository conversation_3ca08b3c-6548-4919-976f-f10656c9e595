# JetBrains Token Manager 部署指南

## 統一啟動架構

本項目現在支持統一啟動方式，可以在 `main.py` 中同時管理前端和後端服務，實現真正的前後端分離但統一管理。

### 支持的部署模式：

1. **完整服務模式（推薦）** - 在 main.py 中統一管理前後端
2. **分離式模式** - 前端和後端分別啟動
3. **Docker 容器模式** - 支持完整和分離兩種容器部署

## 部署方式

### 多服務模式部署

```bash
# 使用預設的docker-compose.yml
docker-compose up -d

# 或明確指定文件
docker-compose -f docker-compose.yml up -d
```

**特點：**
- Daemon和Frontend分離，更好的穩定性
- Frontend崩潰不影響背景任務執行
- 更容易擴展和監控
- 通過共享volume進行服務間通信

### 單服務模式部署

```bash
# 使用單服務配置
docker-compose -f docker-compose.single.yml up -d
```

**特點：**
- 簡單部署，適合小規模使用
- 資源消耗較低
- 向後兼容現有部署

## 服務說明

### JetBrains Daemon (jetbrains-daemon)
- **功能**: 執行背景任務（token刷新、quota檢查）
- **端口**: 無對外端口
- **健康檢查**: 檢查狀態文件是否存在且最近更新
- **配置**: 通過config.json配置
- **日誌**: 輸出到shared-logs volume

### JetBrains Frontend (jetbrains-frontend)
- **功能**: Streamlit Web UI，純展示介面
- **端口**: 8501
- **健康檢查**: HTTP健康檢查
- **依賴**: 依賴daemon服務
- **日誌**: 讀取shared-logs volume中的daemon狀態

## 配置文件

### 必需文件
- `config.json`: 主配置文件（帳戶信息、token等）
- `jetbrainsai.json`: JetBrains AI配置文件

### 可選文件
- `client_api_keys.json`: API客戶端密鑰
- `models.json`: 模型配置

## Volume 掛載

### 多服務模式
```yaml
volumes:
  - ./config.json:/app/config.json:ro          # 配置文件（只讀）
  - ./jetbrainsai.json:/app/jetbrainsai.json   # AI配置文件
  - shared-logs:/app/logs                      # 共享日誌目錄
```

### 單服務模式
```yaml
volumes:
  - ./config.json:/app/config.json:ro          # 配置文件（只讀）
  - ./jetbrainsai.json:/app/jetbrainsai.json   # AI配置文件
  - ./logs:/app/logs                           # 本地日誌目錄
```

## 監控和健康檢查

### Daemon健康檢查
- 檢查狀態文件 `/app/logs/scheduler_status.json` 是否存在
- 檢查狀態文件是否在最近2分鐘內更新

### Frontend健康檢查
- HTTP健康檢查：`http://localhost:8501/_stcore/health`

## 日誌管理

### 多服務模式
- Daemon日誌：容器內部日誌 + 狀態文件
- Frontend日誌：容器內部日誌
- 狀態文件：`shared-logs/scheduler_status.json`

### 單服務模式
- 統一日誌：`./logs/` 目錄
- 狀態文件：`./logs/scheduler_status.json`

## 故障排除

### 檢查服務狀態
```bash
# 檢查所有服務
docker-compose ps

# 檢查特定服務日誌
docker-compose logs jetbrains-daemon
docker-compose logs jetbrains-frontend

# 檢查狀態文件
docker-compose exec jetbrains-daemon cat /app/logs/scheduler_status.json
```

### 常見問題

1. **Daemon無法啟動**
   - 檢查config.json格式是否正確
   - 檢查volume掛載是否正確
   - 查看daemon容器日誌

2. **Frontend無法連接daemon**
   - 檢查shared-logs volume是否正確掛載
   - 檢查狀態文件是否存在
   - 確認daemon服務正在運行

3. **任務不執行**
   - 檢查daemon日誌
   - 檢查config.json中的帳戶配置
   - 確認網絡連接正常

## 升級指南

### 從單服務模式升級到多服務模式

1. 停止現有服務：
   ```bash
   docker-compose -f docker-compose.single.yml down
   ```

2. 備份數據：
   ```bash
   cp -r logs logs.backup
   cp config.json config.json.backup
   ```

3. 啟動多服務模式：
   ```bash
   docker-compose up -d
   ```

4. 驗證服務運行：
   ```bash
   docker-compose ps
   curl http://localhost:8501/_stcore/health
   ```

## 環境變數

- `DAEMON_MODE=true`: 標識daemon模式
- `FRONTEND_MODE=true`: 標識frontend模式
- `DEBUG_MODE=false`: 調試模式（可選）

## 網絡配置

所有服務運行在 `jetbrains-network` 網絡中，支援服務間通信。
