import argparse
import multiprocessing
import signal
import sys
import time
from pathlib import Path

import streamlit.web.cli as stcli

from jetbrains_refresh_token.constants import FRONTEND_APP_PATH
from jetbrains_refresh_token.log_config import get_logger

logger = get_logger("main")


def launch_web_ui(port: int = 8501):
    """
    Launch the Streamlit Web UI.

    Args:
        port (int): Port number for the Web UI. Default is 8501.

    Returns:
        bool: Returns True if launched successfully, otherwise False.
    """
    logger.info("Launching Streamlit Web UI on port %d", port)

    sys.argv = [
        "streamlit",
        "run",
        str(FRONTEND_APP_PATH),
        "--server.port",
        str(port),
        "--server.headless",
        "true",
        "--browser.gatherUsageStats",
        "false",
    ]

    stcli.main()
    return True


def launch_daemon(config_path: str = "config.json", test_mode: bool = False):
    """
    Launch the APScheduler daemon.

    Args:
        config_path (str): Path to the configuration file.
        test_mode (bool): Whether to run in test mode with faster intervals.

    Returns:
        bool: Returns True if launched successfully, otherwise False.
    """
    logger.info("Launching APScheduler daemon with config: %s", config_path)

    try:
        from jetbrains_refresh_token.daemon.scheduler_daemon import JetBrainsDaemon

        daemon = JetBrainsDaemon(config_path=config_path)

        if test_mode:
            logger.info("Running daemon in test mode...")
            from apscheduler.triggers.interval import IntervalTrigger

            # Add test jobs with shorter intervals
            daemon.scheduler.add_job(
                daemon.token_refresh_job,
                trigger=IntervalTrigger(seconds=30),
                id='token_refresh_test',
                name='Token Refresh (Test)',
                replace_existing=True,
            )
            daemon.scheduler.add_job(
                daemon.quota_check_job,
                trigger=IntervalTrigger(minutes=1),
                id='quota_check_test',
                name='Quota Check (Test)',
                replace_existing=True,
            )

        daemon.start()
        return True

    except Exception as e:
        logger.error("Failed to launch daemon: %s", str(e))
        return False


def run_daemon_process(config_path: str, test_mode: bool = False):
    """
    Run daemon in a separate process.

    Args:
        config_path (str): Path to the configuration file.
        test_mode (bool): Whether to run in test mode.
    """
    try:
        launch_daemon(config_path, test_mode)
    except KeyboardInterrupt:
        logger.info("Daemon process received interrupt signal")
    except Exception as e:
        logger.error("Daemon process error: %s", str(e))


def run_web_process(port: int = 8501):
    """
    Run web UI in a separate process.

    Args:
        port (int): Port number for the web UI.
    """
    try:
        launch_web_ui(port)
    except KeyboardInterrupt:
        logger.info("Web UI process received interrupt signal")
    except Exception as e:
        logger.error("Web UI process error: %s", str(e))


class ServiceManager:
    """Manages frontend and backend services with unified lifecycle."""

    def __init__(self):
        self.daemon_process = None
        self.web_process = None
        self.running = False

        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _signal_handler(self, signum, frame):
        """Handle shutdown signals."""
        logger.info(f"Received signal {signum}, shutting down services...")
        self.stop_all()
        sys.exit(0)

    def start_daemon(self, config_path: str = "config.json", test_mode: bool = False):
        """Start the daemon service."""
        if self.daemon_process and self.daemon_process.is_alive():
            logger.warning("Daemon is already running")
            return True

        logger.info("Starting daemon service...")
        self.daemon_process = multiprocessing.Process(
            target=run_daemon_process, args=(config_path, test_mode), name="JetBrains-Daemon"
        )
        self.daemon_process.start()

        # Wait a moment for daemon to initialize
        time.sleep(2)

        if self.daemon_process.is_alive():
            logger.info("Daemon service started successfully (PID: %d)", self.daemon_process.pid)
            return True
        else:
            logger.error("Failed to start daemon service")
            return False

    def start_web(self, port: int = 8501):
        """Start the web UI service."""
        if self.web_process and self.web_process.is_alive():
            logger.warning("Web UI is already running")
            return True

        logger.info("Starting web UI service...")
        self.web_process = multiprocessing.Process(
            target=run_web_process, args=(port,), name="JetBrains-WebUI"
        )
        self.web_process.start()

        # Wait a moment for web UI to initialize
        time.sleep(3)

        if self.web_process.is_alive():
            logger.info("Web UI service started successfully (PID: %d)", self.web_process.pid)
            return True
        else:
            logger.error("Failed to start web UI service")
            return False

    def start_all(
        self, config_path: str = "config.json", web_port: int = 8501, test_mode: bool = False
    ):
        """Start both daemon and web UI services."""
        logger.info("Starting all services...")

        # Start daemon first
        if not self.start_daemon(config_path, test_mode):
            logger.error("Failed to start daemon, aborting")
            return False

        # Start web UI
        if not self.start_web(web_port):
            logger.error("Failed to start web UI, stopping daemon")
            self.stop_daemon()
            return False

        self.running = True
        logger.info("All services started successfully")
        logger.info("Web UI available at: http://localhost:%d", web_port)
        return True

    def stop_daemon(self):
        """Stop the daemon service."""
        if self.daemon_process and self.daemon_process.is_alive():
            logger.info("Stopping daemon service...")
            self.daemon_process.terminate()
            self.daemon_process.join(timeout=10)
            if self.daemon_process.is_alive():
                logger.warning("Force killing daemon process")
                self.daemon_process.kill()
            logger.info("Daemon service stopped")

    def stop_web(self):
        """Stop the web UI service."""
        if self.web_process and self.web_process.is_alive():
            logger.info("Stopping web UI service...")
            self.web_process.terminate()
            self.web_process.join(timeout=10)
            if self.web_process.is_alive():
                logger.warning("Force killing web UI process")
                self.web_process.kill()
            logger.info("Web UI service stopped")

    def stop_all(self):
        """Stop all services."""
        logger.info("Stopping all services...")
        self.running = False
        self.stop_web()
        self.stop_daemon()
        logger.info("All services stopped")

    def wait_for_services(self):
        """Wait for services to complete or be interrupted."""
        try:
            while self.running:
                # Check if processes are still alive
                daemon_alive = self.daemon_process and self.daemon_process.is_alive()
                web_alive = self.web_process and self.web_process.is_alive()

                if not daemon_alive and not web_alive:
                    logger.info("All services have stopped")
                    break
                elif not daemon_alive:
                    logger.error("Daemon service has stopped unexpectedly")
                    self.stop_web()
                    break
                elif not web_alive:
                    logger.error("Web UI service has stopped unexpectedly")
                    self.stop_daemon()
                    break

                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("Received interrupt signal")
            self.stop_all()


def setup_argument_parser():
    parser = argparse.ArgumentParser(
        description='JetBrains JWT Token Refresh Tool',
        epilog='Usage examples:\n'
        '  python main.py --full                    # Start both frontend and backend\n'
        '  python main.py --web                     # Start frontend only\n'
        '  python main.py --daemon                  # Start backend only\n'
        '  python main.py --refresh-access account  # Refresh specific account',
        formatter_class=argparse.RawDescriptionHelpFormatter,
    )

    # Service management arguments
    service_group = parser.add_argument_group('Service Management')
    service_group.add_argument(
        '--full',
        action='store_true',
        help='Launch both frontend and backend services (recommended)',
    )
    service_group.add_argument(
        '--web', action='store_true', help='Launch Streamlit web interface only'
    )
    service_group.add_argument(
        '--daemon', action='store_true', help='Launch APScheduler background service only'
    )
    service_group.add_argument(
        '--web-port', type=int, default=8501, help='Port for web interface (default: 8501)'
    )
    service_group.add_argument(
        '--config',
        type=str,
        default='config.json',
        help='Path to configuration file (default: config.json)',
    )
    service_group.add_argument(
        '--test-mode', action='store_true', help='Run daemon in test mode with faster intervals'
    )

    # Legacy token management arguments
    token_group = parser.add_argument_group('Token Management (Legacy)')
    token_group.add_argument(
        '--refresh-access', type=str, help='Refresh JWT for the specified account'
    )
    token_group.add_argument(
        '--refresh-all-access', action='store_true', help='Refresh JWT for all accounts'
    )
    token_group.add_argument('--backup', action='store_true', help='Backup configuration file')
    token_group.add_argument('--list', action='store_true', help='List all account information')
    token_group.add_argument(
        '--export-jetbrainsai',
        type=str,
        nargs='?',
        const='jetbrainsai.json',
        help='Export configuration to jetbrainsai.json format (optionally specify output path)',
    )
    token_group.add_argument(
        '--check-quota', action='store_true', help='Check quota remaining for all accounts'
    )
    token_group.add_argument(
        '--force', action='store_true', help='Force update tokens (use with refresh options)'
    )

    # Deprecated arguments
    deprecated_group = parser.add_argument_group('Deprecated Arguments')
    deprecated_group.add_argument(
        '--scheduler', action='store_true', help='[DEPRECATED] Use --daemon instead'
    )
    deprecated_group.add_argument(
        '--scheduler-test',
        action='store_true',
        help='[DEPRECATED] Use --daemon --test-mode instead',
    )

    return parser


def main():
    parser = setup_argument_parser()
    args = parser.parse_args()

    # Handle deprecated arguments
    if args.scheduler or args.scheduler_test:
        logger.warning("Scheduler arguments are deprecated.")
        if args.scheduler_test:
            logger.info("Using --daemon --test-mode instead")
            args.daemon = True
            args.test_mode = True
        else:
            logger.info("Using --daemon instead")
            args.daemon = True

    # Service management
    if args.full:
        logger.info("Starting JetBrains Token Manager in full mode...")
        service_manager = ServiceManager()

        success = service_manager.start_all(
            config_path=args.config, web_port=args.web_port, test_mode=args.test_mode
        )

        if success:
            logger.info("All services started successfully")
            logger.info("Press Ctrl+C to stop all services")
            service_manager.wait_for_services()
        else:
            logger.error("Failed to start services")
            sys.exit(1)
        return

    elif args.daemon:
        logger.info("Starting daemon service only...")
        success = launch_daemon(args.config, args.test_mode)
        if not success:
            logger.error("Failed to start daemon service")
            sys.exit(1)
        return

    elif args.web:
        logger.info("Starting web UI service only...")
        success = launch_web_ui(args.web_port)
        if success:
            logger.info("Web UI launched successfully on port %d", args.web_port)
        else:
            logger.error("Failed to launch Web UI. Please check the logs.")
            sys.exit(1)
        return

    # Legacy token management operations
    if (
        args.refresh_access
        or args.refresh_all_access
        or args.backup
        or args.list
        or args.export_jetbrainsai
        or args.check_quota
    ):
        logger.info("Running legacy token management operation...")
        # Import and run legacy operations
        try:
            from jetbrains_refresh_token.core.token_manager import TokenManager
            from jetbrains_refresh_token.utils.config_manager import ConfigManager

            config_manager = ConfigManager(args.config)
            token_manager = TokenManager(config_manager)

            if args.refresh_access:
                success = token_manager.refresh_account_access_token(
                    args.refresh_access, args.force
                )
                if success:
                    logger.info("Token refresh completed successfully")
                else:
                    logger.error("Token refresh failed")
                    sys.exit(1)

            elif args.refresh_all_access:
                success = token_manager.refresh_all_access_tokens(args.force)
                if success:
                    logger.info("All tokens refreshed successfully")
                else:
                    logger.error("Some token refreshes failed")
                    sys.exit(1)

            elif args.backup:
                success = config_manager.backup_config()
                if success:
                    logger.info("Configuration backup completed")
                else:
                    logger.error("Configuration backup failed")
                    sys.exit(1)

            elif args.list:
                accounts = config_manager.get_accounts()
                logger.info("Account list:")
                for account in accounts:
                    logger.info(f"  - {account.get('name', 'Unknown')}")

            elif args.export_jetbrainsai:
                success = config_manager.export_jetbrainsai_format(args.export_jetbrainsai)
                if success:
                    logger.info("JetBrains AI format export completed")
                else:
                    logger.error("JetBrains AI format export failed")
                    sys.exit(1)

            elif args.check_quota:
                success = token_manager.check_all_quotas()
                if success:
                    logger.info("Quota check completed")
                else:
                    logger.error("Quota check failed")
                    sys.exit(1)

        except ImportError as e:
            logger.error("Legacy token management modules not available: %s", str(e))
            logger.error("Please use the web interface (--web) or daemon mode (--daemon)")
            sys.exit(1)
        except Exception as e:
            logger.error("Legacy operation failed: %s", str(e))
            sys.exit(1)

        return

    # Show help if no arguments provided
    if len(sys.argv) == 1:
        logger.info("JetBrains Token Manager")
        logger.info("For full functionality, use: python main.py --full")
        logger.info("For help, use: python main.py --help")
        parser.print_help()
        return


# 主程序入口
if __name__ == "__main__":
    main()
