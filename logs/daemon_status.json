{"daemon_status": "stopped", "start_time": "2025-07-12T11:32:25.351310", "last_update": "2025-07-12T11:32:35.026365", "uptime_seconds": 9.675056, "jobs": {"token_refresh_test": {"name": "<PERSON><PERSON> (Test)", "next_run_time": "2025-07-12T11:32:55.352308+08:00", "trigger": "interval[0:00:30]", "max_instances": 1}, "quota_check_test": {"name": "<PERSON><PERSON><PERSON> (Test)", "next_run_time": "2025-07-12T11:33:25.352374+08:00", "trigger": "interval[0:01:00]", "max_instances": 1}, "health_check": {"name": "Daemon Health Check", "next_run_time": "2025-07-12T11:37:25.352632+08:00", "trigger": "interval[0:05:00]", "max_instances": 1}, "token_refresh": {"name": "JetBrains Token Refresh", "next_run_time": "2025-07-12T12:02:25.352488+08:00", "trigger": "interval[0:30:00]", "max_instances": 1}, "quota_check": {"name": "JetBrains Quota Check", "next_run_time": "2025-07-12T13:32:25.352567+08:00", "trigger": "interval[2:00:00]", "max_instances": 1}}, "job_history": [], "config_path": "config.json", "health": {"status": "unhealthy", "last_check": "2025-07-12T11:32:35.026368"}}